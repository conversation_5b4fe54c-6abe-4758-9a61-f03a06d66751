import Flutter
import UIKit
import BackgroundTasks

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> <PERSON>ol {
    GeneratedPluginRegistrant.register(with: self)

    // Handle deep links
    if let url = launchOptions?[UIApplication.LaunchOptionsKey.url] as? URL {
      return application(application, open: url, options: [:])
    }

    // Register for background fetch
    if #available(iOS 13.0, *) {
      BGTaskScheduler.shared.register(
        forTaskWithIdentifier: "com.quarterlies.backgroundSync",
        using: nil
      ) { task in
        self.handleBackgroundSync(task: task as! BGAppRefreshTask)
      }
    } else {
      // Fallback for older iOS versions
      UIApplication.shared.setMinimumBackgroundFetchInterval(UIApplication.backgroundFetchIntervalMinimum)
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // Handle background fetch for iOS 13+
  @available(iOS 13.0, *)
  func handleBackgroundSync(task: BGAppRefreshTask) {
    // Schedule the next background task
    scheduleBackgroundSync()

    // Create a task to ensure the background task doesn't expire
    let flutterEngine = (UIApplication.shared.delegate as! AppDelegate).flutterEngine
    let backgroundChannel = FlutterMethodChannel(
      name: "com.quarterlies/background_sync",
      binaryMessenger: flutterEngine!.binaryMessenger
    )

    // Call the Flutter method to perform sync
    backgroundChannel.invokeMethod("performBackgroundSync", arguments: nil)

    // Set up a task expiration handler
    task.expirationHandler = {
      // If the task expires, mark it complete
      task.setTaskCompleted(success: false)
    }

    // Set up a completion handler for when the sync is done
    backgroundChannel.setMethodCallHandler { (call, result) in
      if call.method == "syncComplete" {
        task.setTaskCompleted(success: true)
      }
    }
  }

  // Schedule the next background sync task
  @available(iOS 13.0, *)
  func scheduleBackgroundSync() {
    let request = BGAppRefreshTaskRequest(identifier: "com.quarterlies.backgroundSync")
    request.earliestBeginDate = Date(timeIntervalSinceNow: 15 * 60) // 15 minutes

    do {
      try BGTaskScheduler.shared.submit(request)
    } catch {
      print("Could not schedule background sync: \(error)")
    }
  }

  // Handle background fetch for iOS < 13
  override func application(
    _ application: UIApplication,
    performFetchWithCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
  ) {
    let flutterEngine = (UIApplication.shared.delegate as! AppDelegate).flutterEngine
    let backgroundChannel = FlutterMethodChannel(
      name: "com.quarterlies/background_sync",
      binaryMessenger: flutterEngine!.binaryMessenger
    )

    // Call the Flutter method to perform sync
    backgroundChannel.invokeMethod("performBackgroundSync", arguments: nil)

    // Set up a completion handler for when the sync is done
    backgroundChannel.setMethodCallHandler { (call, result) in
      if call.method == "syncComplete" {
        completionHandler(.newData)
      }
    }

    // Set a timeout to ensure the completion handler is called
    DispatchQueue.main.asyncAfter(deadline: .now() + 25) {
      completionHandler(.failed)
    }
  }

  // Handle deep links
  override func application(
    _ app: UIApplication,
    open url: URL,
    options: [UIApplication.OpenURLOptionsKey: Any] = [:]
  ) -> Bool {
    // Check if the URL is a password reset link
    if url.scheme == "io.quarterlies.app" && url.host == "reset-password" {
      // Get the Flutter view controller
      let controller = window?.rootViewController as! FlutterViewController

      // Navigate to the update password screen
      let channel = FlutterMethodChannel(
        name: "com.quarterlies/deep_linking",
        binaryMessenger: controller.binaryMessenger
      )

      channel.invokeMethod("navigate_to_update_password", arguments: nil)
      return true
    }

    return super.application(app, open: url, options: options)
  }
}
